---
description:
globs:
alwaysApply: false
---
# AI导航站后台管理系统 - 开发任务指南

## 当前开发任务清单

### Phase 1 - 权限系统实现 ✅

#### 1.1 创建管理员验证 Hook
```typescript
// hooks/useAdminAuth.ts
import { useQuery } from '@tanstack/react-query';
import { createClient } from '@/lib/supabase/client';

export function useAdminAuth() {
  const supabase = createClient();
  
  return useQuery({
    queryKey: ['admin-auth'],
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return { isAdmin: false };
      
      const { data } = await supabase
        .from('admin_users')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .single();
        
      return { isAdmin: !!data, adminData: data, user };
    }
  });
}
```

#### 1.2 创建路由保护中间件
- 扩展现有的 [middleware.ts](mdc:middleware.ts)
- 添加管理员路由保护逻辑
- 未授权用户重定向到登录页

### Phase 2 - 后台布局实现 🚧

#### 2.1 创建管理后台布局组件
位置: `app/(admin)/layout.tsx`

功能要求:
- 左侧导航栏（固定）
- 顶部标题栏
- 主内容区域
- 响应式设计

#### 2.2 侧边导航组件
位置: `components/admin/sidebar.tsx`

导航项:
```typescript
const navItems = [
  { icon: LayoutDashboard, label: '概览', href: '/admin' },
  { icon: FileCheck, label: '审核', href: '/admin/review' },
  { icon: Users, label: '用户管理', href: '/admin/users' }
];
```

### Phase 3 - 概览页面实现 📋

#### 3.1 统计卡片组件
位置: `components/admin/stats-cards.tsx`

数据获取函数:
```typescript
// lib/admin/stats.ts
export async function getOverviewStats() {
  const supabase = await createClient();
  
  const [tools, categories, todaySubmissions, pendingReviews] = 
    await Promise.all([
      supabase.from('tools').select('*', { count: 'exact', head: true }),
      supabase.from('categories').select('*', { count: 'exact', head: true }),
      supabase.from('tool_submissions')
        .select('*', { count: 'exact', head: true })
        .gte('submitted_at', new Date().toISOString().split('T')[0]),
      supabase.from('tool_submissions')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'pending')
    ]);
    
  return {
    toolsCount: tools.count || 0,
    categoriesCount: categories.count || 0,
    todaySubmissions: todaySubmissions.count || 0,
    pendingReviews: pendingReviews.count || 0
  };
}
```

#### 3.2 工具列表组件
位置: `components/admin/tools-list.tsx`

功能要求:
- 表格展示（使用 shadcn/ui Table）
- 搜索功能
- 分类筛选
- 分页
- 操作按钮（编辑/删除）

#### 3.3 添加工具 Drawer
位置: `components/admin/add-tool-drawer.tsx`

使用 shadcn/ui Sheet 组件实现右侧滑出面板

### Phase 4 - 审核功能实现 🔍

#### 4.1 审核列表页面
位置: `app/(admin)/admin/review/page.tsx`

功能要求:
- 待审核列表展示
- 状态筛选（全部/待审核/已通过/已拒绝）
- 时间筛选
- 详情查看模态框

#### 4.2 审核操作组件
位置: `components/admin/review-actions.tsx`

操作函数:
```typescript
// 审核通过
async function approveSubmission(submissionId: string) {
  const supabase = await createClient();
  
  // 1. 获取提交详情
  const { data: submission } = await supabase
    .from('tool_submissions')
    .select('*')
    .eq('id', submissionId)
    .single();
    
  // 2. 创建工具记录
  const { error: toolError } = await supabase
    .from('tools')
    .insert({
      name: submission.name,
      url: submission.url,
      // ... 其他字段
      status: 'published'
    });
    
  // 3. 更新提交状态
  const { error: updateError } = await supabase
    .from('tool_submissions')
    .update({
      status: 'approved',
      reviewed_by: currentUser.id,
      reviewed_at: new Date().toISOString()
    })
    .eq('id', submissionId);
}
```

### Phase 5 - 用户管理实现 👥

#### 5.1 管理员列表
位置: `app/(admin)/admin/users/page.tsx`

显示字段:
- 用户邮箱
- 角色（admin/super_admin）
- 激活状态
- 加入时间
- 操作按钮

#### 5.2 添加管理员功能
使用 Dialog 组件实现:
- 邮箱搜索用户
- 选择角色
- 确认添加

## 组件开发模板

### 页面组件模板
```typescript
// app/(admin)/admin/[page]/page.tsx
import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import { createClient } from '@/lib/supabase/server';

export default async function AdminPage() {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) redirect('/auth/login');
  
  // 检查管理员权限
  const { data: adminUser } = await supabase
    .from('admin_users')
    .select('*')
    .eq('user_id', user.id)
    .eq('is_active', true)
    .single();
    
  if (!adminUser) redirect('/');
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">页面标题</h1>
        {/* 操作按钮 */}
      </div>
      
      <Suspense fallback={<div>加载中...</div>}>
        {/* 页面内容 */}
      </Suspense>
    </div>
  );
}
```

### 数据表格组件模板
```typescript
// components/admin/data-table.tsx
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
}

export function DataTable<T>({ data, columns }: DataTableProps<T>) {
  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            {columns.map((column) => (
              <TableHead key={column.key}>{column.header}</TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((row, index) => (
            <TableRow key={index}>
              {columns.map((column) => (
                <TableCell key={column.key}>
                  {column.render ? column.render(row) : row[column.key]}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
```

## 最佳实践

### 1. 错误处理
```typescript
try {
  const result = await someOperation();
  toast.success('操作成功');
} catch (error) {
  console.error('操作失败:', error);
  toast.error('操作失败，请重试');
}
```

### 2. 加载状态
- 使用 Suspense 处理异步组件
- 使用 Skeleton 组件显示加载占位
- 操作按钮显示加载状态

### 3. 表单验证
- 使用 react-hook-form + zod
- 客户端和服务端双重验证
- 清晰的错误提示

### 4. 数据缓存
- 使用 React Query 管理服务端状态
- 合理设置缓存时间
- 操作后及时更新缓存

## 测试要点

1. **权限测试**
   - 非管理员无法访问后台
   - 不同角色权限区分

2. **功能测试**
   - CRUD 操作正常
   - 搜索筛选功能
   - 分页功能

3. **UI 测试**
   - 响应式布局
   - 交互反馈
   - 错误处理

## 部署检查清单

- [ ] 环境变量配置正确
- [ ] 数据库表和 RLS 策略已创建
- [ ] 初始管理员账户已添加
- [ ] 错误监控已配置
- [ ] 性能监控已配置
