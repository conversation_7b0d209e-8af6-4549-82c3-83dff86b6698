---
description:
globs:
alwaysApply: false
---
# AI导航站后台管理系统开发规则

## 项目概述

这是一个 AI 导航站的后台管理系统，用于管理 AI 工具、审核用户提交、以及用户权限管理。需求文档详见 [admin-system-prd.md](mdc:docs/admin-system-prd.md)。

## 技术栈

- **前端框架**: React 19 + TypeScript 5
- **UI 组件库**: shadcn/ui (基于 Radix UI)
- **样式**: Tailwind CSS
- **后端**: Supabase (PostgreSQL + Auth + API)
- **状态管理**: React Query / SWR
- **路由**: Next.js 15 App Router
- **部署**: Vercel

## 项目结构

```
ai-tools-navigation-admin/
├── app/                    # Next.js App Router 页面
│   ├── auth/              # 认证相关页面
│   ├── protected/         # 需要认证的页面
│   └── (admin)/          # 管理后台页面（待实现）
├── components/            # React 组件
│   ├── ui/               # shadcn/ui 基础组件
│   └── admin/            # 管理后台专用组件（待实现）
├── lib/                  # 工具函数和配置
│   └── supabase/         # Supabase 客户端配置
└── docs/                 # 项目文档
```

## 数据库设计

### 核心表结构

1. **admin_users** - 管理员用户表
   - 关联 Supabase Auth 用户
   - 角色：admin/super_admin
   - 行级安全策略（RLS）保护

2. **tools** - AI 工具表
   - 已发布的 AI 工具信息
   - 状态：published/archived

3. **tool_submissions** - 工具提交表
   - 用户提交的待审核工具
   - 状态：pending/approved/rejected

4. **categories** - 分类表
   - AI 工具的分类信息

## 开发规范

### 1. 组件开发

```typescript
// 使用函数组件和 TypeScript
interface ComponentProps {
  title: string;
  isActive?: boolean;
}

export function ComponentName({ title, isActive = false }: ComponentProps) {
  // 组件逻辑
}
```

### 2. Supabase 数据操作

```typescript
// 使用类型安全的查询
import { createClient } from "@/lib/supabase/server";

const supabase = await createClient();
const { data, error } = await supabase
  .from('table_name')
  .select('*')
  .eq('field', value);
```

### 3. 权限验证

- 所有管理页面必须验证用户是否在 admin_users 表中
- 使用中间件进行路由保护
- 敏感操作需要二次确认

### 4. UI 设计原则

- 遵循 Apple Design 风格：简洁、清新、科技感
- 使用 shadcn/ui 组件保持一致性
- 响应式设计，支持平板设备

## 功能模块开发指南

### 概览页面 (Overview)

主要组件：
- 统计卡片（工具数量、分类数量、今日提交、待审核）
- AI 工具列表（支持搜索、筛选、分页）
- 添加工具功能（右侧 Drawer）

### 审核页面 (Review)

主要功能：
- 查看待审核提交列表
- 审核通过/拒绝操作
- 批量审核功能（后续扩展）

### 用户管理页面 (User Management)

主要功能：
- 管理员列表展示
- 添加/删除管理员
- 修改权限级别
- 启用/禁用账户

## 开发优先级

1. **Phase 1**: 权限系统 + 基础布局
2. **Phase 2**: 概览页面核心功能
3. **Phase 3**: 审核功能实现
4. **Phase 4**: 用户管理功能

## 注意事项

1. **安全性**
   - 所有 API 调用需要权限验证
   - 使用 Supabase RLS 保护数据
   - 敏感操作记录日志

2. **性能优化**
   - 使用 React Query 缓存数据
   - 大数据列表使用分页
   - 图片使用 Next.js Image 组件优化

3. **错误处理**
   - 统一的错误提示组件
   - 友好的错误信息
   - 操作失败时的回滚机制

## 环境变量

```env
NEXT_PUBLIC_SUPABASE_URL=your-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

## 相关文件

- 需求文档: [admin-system-prd.md](mdc:docs/admin-system-prd.md)
- 主布局: [layout.tsx](mdc:app/layout.tsx)
- Supabase 配置: [server.ts](mdc:lib/supabase/server.ts)
- UI 组件: [components/ui](mdc:components/ui)

## AI 助手提示

在开发此项目时，请：
1. 始终参考需求文档中的详细设计
2. 保持代码风格一致性
3. 使用 TypeScript 确保类型安全
4. 遵循 Next.js 15 App Router 最佳实践
5. 充分利用 shadcn/ui 组件库
6. 确保所有功能都有适当的权限控制
