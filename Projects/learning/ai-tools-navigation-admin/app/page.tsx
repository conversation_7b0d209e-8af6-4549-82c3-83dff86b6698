import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ThemeSwitcher } from "@/components/theme-switcher";
import Link from "next/link";

export default function Home() {
  return (
    <main className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="w-full max-w-md p-6">
        <Card className="shadow-lg">
          <CardHeader className="text-center space-y-4">
            <div className="mx-auto w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center">
              <svg
                className="w-8 h-8 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <CardTitle className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              AI导航站后台管理
            </CardTitle>
            <CardDescription className="text-gray-600 dark:text-gray-400">
              欢迎使用AI导航站后台管理系统，请先登录或注册账户
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Link href="/auth/login" className="block">
              <Button className="w-full" size="lg">
                登录后台
              </Button>
            </Link>
            <Link href="/auth/sign-up" className="block">
              <Button variant="outline" className="w-full" size="lg">
                注册账户
              </Button>
            </Link>
            <div className="text-center text-sm text-gray-500 dark:text-gray-400 mt-6">
              <p>注册的账户默认为普通用户</p>
              <p>需要管理员权限才能访问后台系统</p>
            </div>
          </CardContent>
        </Card>
      </div>

      <footer className="absolute bottom-4 right-4">
        <ThemeSwitcher />
      </footer>
    </main>
  );
}
