import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { checkIsAdmin } from '@/utils/supabase/admin';

export async function GET(request: NextRequest) {
  try {
    // 获取当前用户
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: '用户未登录', isAdmin: false },
        { status: 401 }
      );
    }

    // 检查是否为管理员
    const isAdmin = await checkIsAdmin(user.id);

    return NextResponse.json({
      isAdmin,
      userId: user.id,
      email: user.email
    });

  } catch (error) {
    console.error('检查管理员权限API错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误', isAdmin: false },
      { status: 500 }
    );
  }
}
