import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import { checkIsAdmin } from "@/utils/supabase/admin";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";

export default async function AdminPage() {
  const supabase = await createClient();
  const { data: { user }, error } = await supabase.auth.getUser();

  // 检查用户是否登录
  if (error || !user) {
    redirect('/auth/login');
  }

  // 检查管理员权限
  const isAdmin = await checkIsAdmin(user.id);
  if (!isAdmin) {
    redirect('/');
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto px-4 py-8">
        {/* 头部 */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
              AI导航站后台管理
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              欢迎回来，{user.email}
            </p>
          </div>
          <form action="/auth/signout" method="post">
            <Button variant="outline" type="submit">
              退出登录
            </Button>
          </form>
        </div>

        {/* 功能模块卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* 概览模块 */}
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                概览
              </CardTitle>
              <CardDescription>
                查看系统统计数据和AI工具管理
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/admin/overview">
                <Button className="w-full">
                  进入概览
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* 审核模块 */}
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                审核
              </CardTitle>
              <CardDescription>
                审核用户提交的AI工具
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/admin/review">
                <Button className="w-full">
                  进入审核
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* 用户管理模块 */}
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                  </svg>
                </div>
                用户管理
              </CardTitle>
              <CardDescription>
                管理管理员用户权限
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/admin/users">
                <Button className="w-full">
                  进入用户管理
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>

        {/* 快速统计 */}
        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle>系统状态</CardTitle>
              <CardDescription>
                后台管理系统正在正常运行
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                <p>• 权限验证：正常</p>
                <p>• 数据库连接：正常</p>
                <p>• 管理员权限：已验证</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
