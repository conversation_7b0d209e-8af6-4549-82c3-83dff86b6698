-- 优化后的管理员用户表设计
-- 直接使用 auth.users 的 UUID 作为主键，简化表结构

-- 创建管理员用户表（优化版本）
CREATE TABLE IF NOT EXISTS admin_users (
  user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  role VARCHAR(20) NOT NULL DEFAULT 'admin',
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- 启用行级安全策略
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;

-- 只允许管理员查看管理员列表
CREATE POLICY "admin_read_admin_users"
  ON admin_users
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM admin_users au
      WHERE au.user_id = auth.uid() AND au.is_active = true
    )
  );

-- 仅允许已存在的管理员添加新管理员
CREATE POLICY "admin_insert_admin_users"
  ON admin_users
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM admin_users au
      WHERE au.user_id = auth.uid() AND au.is_active = true
    )
  );

-- 仅允许已存在的管理员移除管理员
CREATE POLICY "admin_delete_admin_users"
  ON admin_users
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM admin_users au
      WHERE au.user_id = auth.uid() AND au.is_active = true
    )
  );

-- 仅允许已存在的管理员更新管理员信息
CREATE POLICY "admin_update_admin_users"
  ON admin_users
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM admin_users au
      WHERE au.user_id = auth.uid() AND au.is_active = true
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM admin_users au
      WHERE au.user_id = auth.uid() AND au.is_active = true
    )
  );

-- 创建索引（优化查询性能）
CREATE INDEX idx_admin_users_is_active ON admin_users(is_active);
CREATE INDEX idx_admin_users_role ON admin_users(role);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_admin_users_updated_at 
    BEFORE UPDATE ON admin_users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 插入第一个管理员用户（需要手动替换 user_id）
-- INSERT INTO admin_users (user_id, role, created_by) 
-- VALUES ('your-user-uuid-here', 'super_admin', 'your-user-uuid-here');

/*
优化说明：

1. 表结构简化：
   - 移除了独立的 id 字段
   - 直接使用 user_id 作为主键
   - 减少了一个索引和字段

2. 性能优化：
   - 查询时直接使用 user_id，无需 JOIN
   - 减少存储空间
   - 简化查询逻辑

3. 业务逻辑清晰：
   - 一个用户对应一条管理员记录
   - 关系更直观，代码更简洁

4. 使用方式：
   - 检查管理员：SELECT * FROM admin_users WHERE user_id = $1 AND is_active = true
   - 添加管理员：INSERT INTO admin_users (user_id, role) VALUES ($1, 'admin')
   - 删除管理员：DELETE FROM admin_users WHERE user_id = $1
*/
