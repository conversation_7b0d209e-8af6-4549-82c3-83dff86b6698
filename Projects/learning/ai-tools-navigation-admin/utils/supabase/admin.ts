import { createClient } from '@supabase/supabase-js';

// 创建管理员客户端，使用服务密钥绕过RLS策略
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

/**
 * 检查用户是否为管理员
 * @param userId - 用户ID
 * @returns Promise<boolean> - 是否为管理员
 */
export async function checkIsAdmin(userId: string): Promise<boolean> {
  try {
    const { data, error } = await supabaseAdmin
      .from('admin_users')
      .select('user_id')
      .eq('user_id', userId)
      .eq('is_active', true)
      .single();

    if (error) {
      // 如果是找不到记录的错误，说明不是管理员，这是正常情况
      if (error.code === 'PGRST116') {
        return false;
      }
      console.error('检查管理员权限时出错:', error);
      return false;
    }

    return !!data;
  } catch (error) {
    console.error('检查管理员权限异常:', error);
    return false;
  }
}

/**
 * 获取管理员用户信息
 * @param userId - 用户ID
 * @returns Promise<AdminUser | null> - 管理员用户信息
 */
export async function getAdminUser(userId: string) {
  try {
    const { data, error } = await supabaseAdmin
      .from('admin_users')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true)
      .single();

    if (error) {
      // 如果是找不到记录的错误，返回null
      if (error.code === 'PGRST116') {
        return null;
      }
      console.error('获取管理员信息时出错:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('获取管理员信息异常:', error);
    return null;
  }
}

export { supabaseAdmin };
